<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>


  <script>
    const TriggerType = Object.freeze({
      SET: 'SET',
      ADD: 'ADD',
      DELETE: 'DELETE'
    })

    const ITERATE_KEY = Symbol()

    let activeEffect

    const effectStack = []

    function effect (fn, options = {}) {

      const effectFn = () => {
        cleanup(effectFn)
        activeEffect = effectFn
        effectStack.push(effectFn)
        const res = fn()
        effectStack.pop()

        activeEffect = effectStack[effectStack.length - 1]

        return res
      }


      effectFn.options = options
      effectFn.deps = []

      if (!effectFn.options.lazy) {
        effectFn()
      }

      return effectFn
    }

    function cleanup (effectFn) {
      for (let i = 0; i < effectFn.deps.length; i++) {
        const deps = effectFn.deps[i]
        deps.delete(effectFn)
      }

      effectFn.deps.length = 0
    }

    const bucket = new WeakMap()

    const track = (target, key) => {
      if (!activeEffect) {
        return target[key]
      }

      // 1. 获取当前对象对应的依赖集合
      let depsMap = bucket.get(target)

      if (!depsMap) {
        depsMap = new Map()
        bucket.set(target, depsMap)
      }

      // 2. 获取当前属性对应的依赖集合
      let deps = depsMap.get(key)

      if (!deps) {
        deps = new Set()
        depsMap.set(key, deps)
      }

      deps.add(activeEffect)

      activeEffect.deps.push(deps)

    }

    const trigger = (target, key, type) => {
      // 1. 获取当前对象对应的依赖集合
      const depsMap = bucket.get(target)
      if (!depsMap) return

      // 2. 获取当前属性对应的依赖集合
      const effects = depsMap.get(key)

      // 3. 执行依赖函数
      const effectsToRun = new Set()
      effects && effects.forEach((fn) => {
        if (activeEffect !== fn) { // 避免自己执行自己 无限递归爆栈
          effectsToRun.add(fn)
        }
      })

      if (type === TriggerType.ADD || type === TriggerType.DELETE) { // 如果是添加 或 删除
        const iterateEffects = depsMap.get(ITERATE_KEY)
        iterateEffects && iterateEffects.forEach((effectFn) => {
          if (activeEffect !== effectFn) { // 避免自己执行自己 无限递归爆栈
            effectsToRun.add(effectFn)
          }
        })
      }

      effectsToRun && effectsToRun.forEach((effectFn) => {
        // 如果存在调度器 则调用调度器 把函数作为参数传入
        if (effectFn.options.scheduler) {
          effectFn.options.scheduler(effectFn)
        } else {
          effectFn()
        }
      })
    }


    function createReactive (obj, isShallow = false) {
      return new Proxy(obj, {
        get (target, key, receiver) {

          // 代理对象可以通过 raw 属性访问原始数据
          if (key === 'raw') {
            return target
          }

          const  res = Reflect.get(target, key, receiver)

          track(target, key)

          // 浅响应 直接返回对象
          if (isShallow) {
            return res
          }

          // 如果是对象则继续递归 返回响应式数据
          if(typeof res === 'object' && res !== null){
            return reactive(res)
          }

          return res
        },

        set (target, key, newValue, receiver) {
          // 获取旧值
          const oldValue = target[key]

          const type = Object.prototype.hasOwnProperty.call(target, key) ?
            TriggerType.SET : TriggerType.ADD //

          const res = Reflect.set(target, key, newValue, receiver)


          // 代理对象可以通过 raw 属性访问原始数据
          if (target === receiver.raw) {
            // 考虑新旧值变化问题，且NAN 问题
            // NaN !== NaN  true
            // NaN === NaN  false
            if (newValue !== oldValue && (oldValue === oldValue || newValue === newValue)) {
              trigger(target, key, type)
            }
          }

          return res
        },

        deleteProperty (target, key) {
          const hadKey = Object.prototype.hasOwnProperty.call(target, key)
          const res = Reflect.deleteProperty(target, key)
          if (res && hadKey) {
            trigger(target, key, TriggerType.DELETE)
          }
          return res
        },

        has (target, key) {
          track(target, key)
          return Reflect.has(target, key)
        },

        ownKeys (target) {
          track(target, ITERATE_KEY)
          return Reflect.ownKeys(target)
        }

      })
    }

    function reactive (obj) {
      return createReactive(obj)
    }

    function shallowReactive(obj){
      return createReactive(obj,true)
    }

    const obj= {
      foo:{
        bar:1
      }
    }

    const reactiveObj = reactive(obj)
    const shallowReactiveObj = shallowReactive(obj)

    effect(()=>{
      console.log(reactiveObj.foo.bar)
    })

    reactiveObj.foo.bar = 2


  </script>
</body>

</html>