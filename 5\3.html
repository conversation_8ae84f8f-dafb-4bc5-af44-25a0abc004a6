<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>


  <script>
    const TriggerType = Object.freeze({
      SET: 'SET',
      ADD: 'ADD',
      DELETE: 'DELETE'
    })

    const ITERATE_KEY = Symbol()

    let activeEffect

    const effectStack = []

    function effect (fn, options = {}) {

      const effectFn = () => {
        cleanup(effectFn)
        activeEffect = effectFn
        effectStack.push(effectFn)
        const res = fn()
        effectStack.pop()

        activeEffect = effectStack[effectStack.length - 1]

        return res
      }


      effectFn.options = options
      effectFn.deps = []

      if (!effectFn.options.lazy) {
        effectFn()
      }

      return effectFn
    }

    function cleanup (effectFn) {
      for (let i = 0; i < effectFn.deps.length; i++) {
        const deps = effectFn.deps[i]
        deps.delete(effectFn)
      }

      effectFn.deps.length = 0
    }

    const bucket = new WeakMap()

    const track = (target, key) => {
      if (!activeEffect) {
        return target[key]
      }

      // 1. 获取当前对象对应的依赖集合
      let depsMap = bucket.get(target)

      if (!depsMap) {
        depsMap = new Map()
        bucket.set(target, depsMap)
      }

      // 2. 获取当前属性对应的依赖集合
      let deps = depsMap.get(key)

      if (!deps) {
        deps = new Set()
        depsMap.set(key, deps)
      }

      deps.add(activeEffect)

      activeEffect.deps.push(deps)

    }

    const trigger = (target, key, type) => {
      // 1. 获取当前对象对应的依赖集合
      const depsMap = bucket.get(target)
      if (!depsMap) return

      // 2. 获取当前属性对应的依赖集合
      const effects = depsMap.get(key)

      // 3. 执行依赖函数
      const effectsToRun = new Set()
      effects && effects.forEach((fn) => {
        if (activeEffect !== fn) { // 避免自己执行自己 无限递归爆栈
          effectsToRun.add(fn)
        }
      })

      if (type === TriggerType.ADD || type === TriggerType.DELETE) { // 如果是添加 或 删除
        const iterateEffects = depsMap.get(ITERATE_KEY)
        iterateEffects && iterateEffects.forEach((effectFn) => {
          if (activeEffect !== effectFn) { // 避免自己执行自己 无限递归爆栈
            effectsToRun.add(effectFn)
          }
        })
      }

      effectsToRun && effectsToRun.forEach((effectFn) => {
        // 如果存在调度器 则调用调度器 把函数作为参数传入
        if (effectFn.options.scheduler) {
          effectFn.options.scheduler(effectFn)
        } else {
          effectFn()
        }
      })
    }


    const obj = {
      foo: 1,
    }

    const p = new Proxy(obj, {
      get (target, key, receiver) {
        track(target, key)
        return Reflect.get(target, key, receiver)
      },

      set (target, key, newValue, receiver) {
        // 获取旧值
        const oldValue = target[key]

        const type = Object.prototype.hasOwnProperty.call(target, key) ?
          TriggerType.SET : TriggerType.ADD //

        const res = Reflect.set(target, key, newValue, receiver)
        trigger(target, key, type)
        return res
      },

      deleteProperty (target, key) {
        const hadKey = Object.prototype.hasOwnProperty.call(target, key)
        const res = Reflect.deleteProperty(target, key)
        if (res && hadKey) {
          trigger(target, key, TriggerType.DELETE)
        }
        return res
      },

      has (target, key) {
        track(target, key)
        return Reflect.has(target, key)
      },

      ownKeys (target) {
        track(target, ITERATE_KEY)
        return Reflect.ownKeys(target)
      }

    })

    effect(() => {
      for (const key in p) {
        console.log('%c [  ]-160', 'font-size:13px; background:pink; color:#bf2c9f;', key);
      }
    })
    p.bar = 3
    p.bar = 4

    delete p.bar

  </script>
</body>

</html>