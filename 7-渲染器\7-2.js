
// 创建渲染器
function createRenderer (options) {

  //通过options 拿到对应操作的api
  const {
    createElement,
    setElementText,
    insert,
  } = options

  // 挂载元素
  function mountElement (vnode, container) {
    // 创建type类型元素
    const el = createElement(vnode.type);

    // 处理子节点 如果是string  表示元素有文本节点
    if (typeof vnode.children === 'string') {
      // 设置文本节点
      setElementText(el, vnode.children);
    }

    // 将元素添加到容器中
    insert(el, container);
  }



  /**
   * n1 @param {*} 旧vnode
   * n2 @param {*} 新vnode
   * container @param {*} 容器
   *  在这里编写渲染逻辑
   */
  function patch (n1, n2, container) {
    // 如果旧vnode 不存在 就表示挂载
    if (!n1) {
      // 挂载
      mountElement(n2, container)
    } else {
      // 打补丁  暂时省略
    }
  }



  function render (vnode, container) {
    if (vnode) {
      // 新 节点存在， 就将旧vnode 一起传给patch 函数 进行打补丁
      // container._vnode 表示旧vnode
      patch(container._vnode, vnode, container)
    } else {
      if (container._vnode) {
        // 只有旧vnode 存在，  没有新vnode   就是卸载操作
        container.innerHTML = '';
      }
    }

    // 存vnode 作为下一个的旧vnode
    container._vnode = vnode;
  }

  // 服务端渲染内容时讲解
  function hydrate (vnode, container) {
  }

  return {
    render,
    hydrate
  }
}


// const renderer = createRenderer({
//   createElement(tag){
//     return document.createElement(tag);
//   },
//   setElementText(el,text){
//     el.textContent = text;
//   },
//   insert(el,parent,anchor=null){
//     parent.insertBefore(el,anchor);
//   }
// })
// renderer.render(vnode, document.querySelector('#app'))


const renderer = createRenderer({
  createElement (tag) {

    console.log('%c [  ]-90', 'font-size:13px; background:pink; color:#bf2c9f;', `创建元素 ${tag}`)
    return { tag }
  },

  setElementText (el, text) {

    console.log('%c [  ]-96', 'font-size:13px; background:pink; color:#bf2c9f;',
      `设置 ${JSON.stringify(el)} 的文本内容 ：${text}`
    )
    el.textContent = text
  },
  insert (el, parent, anchor = null) {

    console.log('%c [  ]-103', 'font-size:13px; background:pink; color:#bf2c9f;',
      `将 ${JSON.stringify(el)} 插入到 ${JSON.stringify(parent)} 中`
    )
    parent.children = el
    console.log('%c [ parent ]-108', 'font-size:13px; background:pink; color:#bf2c9f;', parent)
  }
})


const vnode = {
  type: 'h1',
  children: 'hello world'
}

const root = {
  type :'root'
}

renderer.render(vnode, root)

