<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Document</title>
	</head>
	<body>
		<script>
			let activeEffect

			function effect(fn) {
				// activeEffect = fn
        // fn()
        const effectFn = () => {
          cleanup(effectFn)
          activeEffect = effectFn
          fn()
        }
        effectFn.deps = []
        effectFn()
			}

      function cleanup (effectFn){
        for(let i=0;i<effectFn.deps.length;i++){
          const deps = effectFn.deps[i]
          deps.delete(effectFn)
        }

        effectFn.deps.length = 0
      }

			const bucket = new WeakMap()

			const track = (target, key) => {

        console.log('%c [1  ]-21', 'font-size:13px; background:pink; color:#bf2c9f;', );
				if (!activeEffect) {
					return target[key]
				}


        console.log('%c [ 2 ]-27', 'font-size:13px; background:pink; color:#bf2c9f;', );
				// 1. 获取当前对象对应的依赖集合
				let depsMap = bucket.get(target)
        
				if (!depsMap) {
          depsMap = new Map()
					bucket.set(target, depsMap)
				}


        console.log('%c [ 3 ]-37', 'font-size:13px; background:pink; color:#bf2c9f;', );
				// 2. 获取当前属性对应的依赖集合
				let deps = depsMap.get(key)
        
				if (!deps) {
          deps = new Set()
					depsMap.set(key, deps)
				}
        

        console.log('%c [ 4 ]-47', 'font-size:13px; background:pink; color:#bf2c9f;', );
				deps.add(activeEffect)

        activeEffect.deps.push(deps)

			}

			const trigger = (target, key) => {
				// 1. 获取当前对象对应的依赖集合
				const depsMap = bucket.get(target)
				if (!depsMap) return

				// 2. 获取当前属性对应的依赖集合
				const effects = depsMap.get(key)

				// 3. 执行依赖函数
        const effectsToRun = new Set(effects)
        effectsToRun && effectsToRun.forEach((fn) => fn())
				// effects && effects.forEach((fn) => fn())
			}

			const data = {ok:true, text: 'hello world' }

			const obj = new Proxy(data, {
				get(target, key) {
					track(target, key)

					return target[key]
				},

				set(target, key, newVal) {
					target[key] = newVal
					trigger(target, key)
				},
			})

			// effect(()=>{
			//   document.body.innerHTML = obj.text
			// })

			effect(() => {

        document.body.innerHTML = obj.ok? obj.text : 'not'
        console.log('%c [  ]-85', 'font-size:13px; background:pink; color:#bf2c9f;', '--------------');
			})

			setTimeout(() => {
        // obj.noExist = 'hello vue3'
        obj.ok = false        
			}, 1000)

      setTimeout(()=>{
				obj.text = 'hello vue3'

      },2000)
		</script>
	</body>
</html>
