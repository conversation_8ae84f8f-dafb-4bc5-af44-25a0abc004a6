<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>


  <script>
    let activeEffect

    const effectStack = []

    function effect (fn, options = {}) {

      const effectFn = () => {
        cleanup(effectFn)
        activeEffect = effectFn
        effectStack.push(effectFn)
        const res = fn()
        effectStack.pop()

        activeEffect = effectStack[effectStack.length - 1]

        return res
      }


      effectFn.options = options
      effectFn.deps = []

      if (!effectFn.options.lazy) {
        effectFn()
      }

      return effectFn
    }

    function cleanup (effectFn) {
      for (let i = 0; i < effectFn.deps.length; i++) {
        const deps = effectFn.deps[i]
        deps.delete(effectFn)
      }

      effectFn.deps.length = 0
    }

    const bucket = new WeakMap()

    const track = (target, key) => {
      if (!activeEffect) {
        return target[key]
      }

      // 1. 获取当前对象对应的依赖集合
      let depsMap = bucket.get(target)

      if (!depsMap) {
        depsMap = new Map()
        bucket.set(target, depsMap)
      }

      // 2. 获取当前属性对应的依赖集合
      let deps = depsMap.get(key)

      if (!deps) {
        deps = new Set()
        depsMap.set(key, deps)
      }

      deps.add(activeEffect)

      activeEffect.deps.push(deps)

    }

    const trigger = (target, key) => {
      // 1. 获取当前对象对应的依赖集合
      const depsMap = bucket.get(target)
      if (!depsMap) return

      // 2. 获取当前属性对应的依赖集合
      const effects = depsMap.get(key)

      // 3. 执行依赖函数
      const effectsToRun = new Set()
      effects && effects.forEach((fn) => {
        if (activeEffect !== fn) { // 避免自己执行自己 无限递归爆栈
          effectsToRun.add(fn)
        }
      })

      effectsToRun && effectsToRun.forEach((effectFn) => {
        // 如果存在调度器 则调用调度器 把函数作为参数传入
        if (effectFn.options.scheduler) {
          effectFn.options.scheduler(effectFn)
        } else {
          effectFn()
        }
      })
    }

    
    const obj = {
      foo: 1,
      get bar(){
        return this.foo
      }
    }

    const p = new Proxy(obj, {
      deleteProperty(target, key) {
        delete target[key]
        return Reflect.deleteProperty(target, key)
      }
    })


    console.log('%c [  ]-123', 'font-size:13px; background:pink; color:#bf2c9f;', p.foo);
    delete p.foo

    console.log('%c [  ]-126', 'font-size:13px; background:pink; color:#bf2c9f;', p.foo);


  </script>
</body>

</html>