<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Document</title>
	</head>
	<body>
		<script>
      let activeEffect;

			function effect(fn) {
				activeEffect = fn 
        fn()
			}

			const bucket = new Set()

			const data = { text: 'hello world' }

			const obj = new Proxy(data, {
				get(target, key) {
					if(activeEffect){
            bucket.add(activeEffect)
          }

					return target[key]
				},

				set(target, key, newVal) {
					target[key] = newVal
					bucket.forEach((fn) => fn())
					// return true 代表操作成功
					return true
				},
			})

			// effect(()=>{
      //   document.body.innerHTML = obj.text
      // })

      effect(()=>{
        
        console.log('%c [  ]-44', 'font-size:13px; background:pink; color:#bf2c9f;', 'effect run ');
        document.body.innerHTML = obj.text  
      })

			setTimeout(() => {
				// obj.text = 'hello vue3'
				obj.noExist  = 'hello vue3'
			}, 1000)
		</script>
	</body>
</html>
