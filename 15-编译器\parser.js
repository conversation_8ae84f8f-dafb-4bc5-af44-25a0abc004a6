// parser 相关

// 定义状态机的状态
const State = {
    initial: 1, // 初始状态
    tagOpen: 2, // 标签开始状态
    tagName: 3, // 标签名称状态
    text: 4, // 文本状态
    tagEnd: 5, // 结束标签状态
    tagEndName: 6, // 结束标签名称状态
}

function isAlpha (char) {
    return (char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z');
}

// 接收模板字符串作为参数，并将模板切割为 Token 返回

function tokenize (str) {
    // 状态机当前的状态：初始状态
    let currentState = State.initial;

    // 用于缓存 字符
    const chars = []

    // 用于缓存 token 类型和值 
    const tokens = []

    while (str) {
        const char = str[0];
        switch (currentState) {
            case State.initial:
                if (char === '<') {
                    currentState = State.tagOpen;
                    str = str.slice(1);
                } else if (isAlpha(char)) {
                    currentState = State.text;
                    chars.push(char);
                    str = str.slice(1);
                }
                break;

            case State.tagOpen:
                if (isAlpha(char)) {
                    currentState = State.tagName;
                    chars.push(char);
                    str = str.slice(1);
                } else if (char === '/') {
                    currentState = State.tagEnd;
                    str = str.slice(1);
                }
                break;

            case State.tagName:
                if (char === '>') {
                    currentState = State.initial;
                    // 创建标签Token 存入Tokens
                    tokens.push({
                        type: 'tag',
                        name: chars.join(''),
                    });
                    // 清空 chars
                    chars.length = 0;
                    str = str.slice(1);
                } else if (isAlpha(char)) {
                    chars.push(char);
                    str = str.slice(1);
                }
                break;

            case State.text:
                if (isAlpha(char)) {
                    // 遇到字母状态不改变，
                    chars.push(char);
                    str = str.slice(1);
                } else if (char === '<') {
                    currentState = State.tagOpen;

                    tokens.push({
                        type: 'text',
                        content: chars.join(''),
                    });
                    chars.length = 0;
                    str = str.slice(1);
                }
                break;

            case state.tagEnd:
                if (isAlpha(char)) {
                    currentState = State.tagEndName;
                    chars.push(char);
                    str = str.slice(1);
                }
                break;

            case state.tagEndName:
                if (isAlpha(char)) {
                    chars.push(char);
                    str = str.slice(1);
                } else if (char === '>') {
                    currentState = State.initial;
                    tokens.push({
                        type: 'tagEnd',
                        name: chars.join(''),
                    });
                    chars.length = 0;
                    str = str.slice(1);
                }
                break;
        }
    }
    return tokens;
}

const tokens = tokenize(`<div>hello</div>`);
console.log('%c [ tokens ]-116', 'font-size:13px; background:pink; color:#bf2c9f;', tokens)




