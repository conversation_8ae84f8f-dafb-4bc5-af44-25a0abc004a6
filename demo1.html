<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>
<body>
  


  <script>
    function effect(){
      document.body.innerHTML = obj.text
    }
    
    
    const  bucket = new Set()
    
    const data= {text:'hello world'}
    
    const obj = new Proxy(data,{
      get(target,key){
        bucket.add(effect)

        return target[key]
      },

      set(target,key,newVal){
        target[key] = newVal
        bucket.forEach(fn=>fn())
        // return true 代表操作成功
        return true
      }
    })

    effect()

    setTimeout(()=>{
      obj.text = 'hello vue3'
    },1000)
    
    </script>
</body>

</html>

